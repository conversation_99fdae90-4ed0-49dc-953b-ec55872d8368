package fm.lizhi.ocean.wavecenter.api.activitycenter.constants;

import lombok.Getter;

/**
 * 奖励标签
 * <AUTHOR>
 */
@Getter
public enum RewardTagEnum {

    /** 推荐卡 */
    REC_CARD(AutoConfigResourceEnum.REC_CARD.getResourceCode(), AutoConfigResourceEnum.REC_CARD.getResourceName()),

    /** banner */
    BANNER(AutoConfigResourceEnum.BANNER.getResourceCode(), AutoConfigResourceEnum.BANNER.getResourceName()),

    /** 官频位 */
    OFFICIAL_SEAT(AutoConfigResourceEnum.OFFICIAL_SEAT.getResourceCode(), AutoConfigResourceEnum.OFFICIAL_SEAT.getResourceName()),

    /** 返场礼物 */
    GIFT("gift", "返场礼物"),

    /** 专属挂件 */
    PENDANT("pendant", "专属挂件"),

    /** 福袋 */
    LUCKY_BAG("lucky_bag", "福袋"),

    /** 角标 */
    ROOM_MARK("room_mark", "角标")
    ;

    RewardTagEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private final String code;

    private final String name;
}
