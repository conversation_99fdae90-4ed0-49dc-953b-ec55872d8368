package fm.lizhi.ocean.wavecenter.infrastructure.gift.remote;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.bean.PageBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.api.gift.bean.ListGiftBean;
import fm.lizhi.ocean.wavecenter.api.gift.request.RequestListGift;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.JoinerUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.gift.convert.GiftInfraConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.gift.convert.HyGiftConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.gift.datastore.entity.GiftPo;
import fm.lizhi.ocean.wavecenter.infrastructure.gift.datastore.mapper.HyLiveGiftMapper;
import fm.lizhi.ocean.wavecenter.service.gift.dto.CreateGiftTimeParam;
import fm.lizhi.ocean.wavecenter.service.gift.dto.CreateGiftTimeResult;
import fm.lizhi.ocean.wavecenter.service.gift.dto.DeleteGiftTimeParam;
import fm.lizhi.ocean.wavecenter.service.gift.dto.GiftDto;
import hy.fm.lizhi.live.gift.api.GiftGroupService;
import hy.fm.lizhi.live.gift.api.GiftListService;
import hy.fm.lizhi.live.gift.api.GiftManagerService;
import hy.fm.lizhi.live.gift.api.GiftNewService;
import hy.fm.lizhi.live.gift.constants.GiftType;
import hy.fm.lizhi.live.gift.protocol.BeanProto;
import hy.fm.lizhi.live.gift.protocol.GiftGroupProto;
import hy.fm.lizhi.live.gift.protocol.GiftListProto;
import hy.fm.lizhi.live.gift.protocol.GiftNewManagerProto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/4/24 15:45
 */
@Slf4j
@Component
public class HyGiftRemote implements IGiftRemote{

    @Autowired
    private HyLiveGiftMapper liveGiftMapper;

    @Autowired
    private GiftNewService giftNewService;

    @Autowired
    private GiftListService giftListService;

    @Autowired
    private GiftManagerService giftManagerService;

    @Autowired
    private GiftGroupService giftGroupService;

    @Autowired
    private GiftInfraConvert giftInfraConvert;

    @Autowired
    private HyGiftConvert hyGiftConvert;

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.HEI_YE;
    }

    @Override
    public List<GiftDto> getByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        List<GiftPo> list = liveGiftMapper.getByIds(ids);
        return giftInfraConvert.toGiftDtoList(list, BusinessEvnEnum.HEI_YE.appId());
    }

    @Override
    public Result<PageBean<ListGiftBean>> listGift(RequestListGift param) {
        log.info("HyGiftRemote listGift param: {}", param);
        GiftNewManagerProto.GiftListQueryRequest.Builder builder = GiftNewManagerProto.GiftListQueryRequest.newBuilder();
        builder.addGiftType(GiftType.NEW_GIFT.getValue());
        builder.setCurrentPage(param.getPageNo());
        builder.setPageSize(param.getPageSize());
        Optional.ofNullable(param.getId()).ifPresent(builder::setGiftId);
        Optional.ofNullable(param.getName()).ifPresent(builder::setName);
        Optional.ofNullable(param.getStatus()).ifPresent(builder::setGiftStatus);
        GiftNewManagerProto.GiftListQueryRequest request = builder.build();
        Result<GiftNewManagerProto.ResponsePageQueryGiftListAndSort> result = giftNewService.pageQueryGiftListAndSort(request);
        if (RpcResult.isFail(result)) {
            return RpcResult.fail(result.rCode());
        }
        GiftNewManagerProto.ResponsePageQueryGiftListAndSort response = result.target();
        log.debug("HyGiftRemote listGift response: {}", response);
        PageBean<ListGiftBean> pageBean = hyGiftConvert.toListGiftPageBean(response);
        return RpcResult.success(pageBean);
    }

    @Override
    public Result<Void> updateWhiteNjGroupId(long giftId, long groupId) {
        log.info("HyGiftRemote updateWhiteNjGroupId giftId: {}, groupId: {}", giftId, groupId);
        Result<GiftListProto.ResponseGetGift> getGiftResult = giftListService.getGift(giftId);
        if (RpcResult.isFail(getGiftResult)) {
            log.info("HyGiftRemote updateWhiteNjGroupId getGift fail, rCode: {}, giftId: {}",
                    getGiftResult.rCode(), giftId);
            return RpcResult.fail(getGiftResult.rCode(), "查询礼物失败, giftId: " + giftId);
        }
        BeanProto.Gift oldGift = getGiftResult.target().getGift();
        long oldGroupId = oldGift.getGiftShowLimit().getWhiteNjidGroupId();
        if (Objects.equals(oldGroupId, groupId)) {
            // 如果已经相等, 视为成功
            log.info("HyGiftRemote updateWhiteNjGroupId giftId: {} already equal groupId: {}", giftId, groupId);
            return RpcResult.success();
        }
        BeanProto.GiftShowLimit newShowLimit = BeanProto.GiftShowLimit.newBuilder(oldGift.getGiftShowLimit())
                .setWhiteNjidGroupId(groupId)
                .setLimitStatus(1)
                .build();
        BeanProto.Gift newGift = BeanProto.Gift.newBuilder(oldGift)
                .setGiftShowLimit(newShowLimit)
                .build();
        Result<Void> editGiftResult = giftManagerService.editGift(newGift);
        if (RpcResult.isFail(editGiftResult)) {
            log.error("HyGiftRemote updateWhiteNjGroupId editGift fail, rCode: {}, newGift: {}",
                    editGiftResult.rCode(), newGift);
            return RpcResult.fail(editGiftResult.rCode(), "更新礼物白名单主播组失败, 礼物ID: " + giftId + ", 分组ID: " + groupId);
        }
        log.info("HyGiftRemote updateWhiteNjGroupId success, giftId: {}, groupId: {}, oldGroupId: {}, newGift: {}",
                giftId, groupId, oldGroupId, newGift);
        return RpcResult.success();
    }

    @Override
    public Result<Void> addGiftToGroup(long giftId, long groupId) {
        log.info("HyGiftRemote addGiftToGroup giftId: {}, groupId: {}", giftId, groupId);
        Result<BeanProto.GiftGroup> getGiftGroupResult = this.getGiftGroup(groupId);
        if (RpcResult.isFail(getGiftGroupResult)) {
            return RpcResult.fail(getGiftGroupResult.rCode(), getGiftGroupResult.getMessage());
        }
        BeanProto.GiftGroup oldGiftGroup = getGiftGroupResult.target();
        String oldRelationGift = oldGiftGroup.getRelationGift();
        List<Long> oldGiftIds = JoinerUtils.splitLong(oldRelationGift);
        if (oldGiftIds.contains(giftId)) {
            // 如果已经存在, 视为成功
            log.info("HyGiftRemote addGiftToGroup giftId: {} already in groupId: {}", giftId, groupId);
            return RpcResult.success();
        }
        ArrayList<Long> newGiftIds = new ArrayList<>(oldGiftIds);
        newGiftIds.add(giftId);
        String newRelationGift = JoinerUtils.join(newGiftIds);
        BeanProto.GiftGroup newGiftGroup = BeanProto.GiftGroup.newBuilder(oldGiftGroup)
                .setRelationGift(newRelationGift)
                .build();
        Result<Void> mergeGiftGroupResult = giftGroupService.mergeGiftGroup(newGiftGroup);
        if (RpcResult.isFail(mergeGiftGroupResult)) {
            log.error("HyGiftRemote addGiftToGroup mergeGiftGroup fail, rCode: {}, newGiftGroup: {}",
                    mergeGiftGroupResult.rCode(), newGiftGroup);
            return RpcResult.fail(mergeGiftGroupResult.rCode(), "添加礼物到分组失败, 礼物ID: " + giftId + ", 分组ID: " + groupId);
        }
        log.info("HyGiftRemote addGiftToGroup success, giftId: {}, groupId: {}", giftId, groupId);
        return RpcResult.success();
    }

    private Result<BeanProto.GiftGroup> getGiftGroup(long groupId) {
        // 财富等级传入最大值, 以保证查到分组
        Result<GiftGroupProto.ResponseGiftGroup> result = giftGroupService
                .queryGiftByGroup(Collections.singletonList(groupId), Integer.MAX_VALUE);
        if (RpcResult.isFail(result)) {
            log.error("HyGiftRemote getGiftGroup fail groupId: {}, rCode: {}", groupId, result.rCode());
            return RpcResult.fail(result.rCode(), "查询礼物分组失败, groupId: " + groupId);
        }
        for (BeanProto.GiftGroup giftGroup : result.target().getGiftGroupListList()) {
            if (Objects.equals(giftGroup.getId(), groupId)) {
                log.info("HyGiftRemote getGiftGroup groupId: {}, giftGroup: {}", groupId, giftGroup);
                return RpcResult.success(giftGroup);
            }
        }
        log.warn("HyGiftRemote getGiftGroup groupId: {}, giftGroup not found", groupId);
        return RpcResult.fail(CommonService.PARAM_ERROR, "礼物分组不存在");
    }

    @Override
    public Result<CreateGiftTimeResult> createGiftTime(CreateGiftTimeParam param) {
        log.info("HyGiftRemote createGiftTiming param: {}", param);
        GiftNewManagerProto.GiftTime giftTime = GiftNewManagerProto.GiftTime.newBuilder()
                .setGiftId(param.getGiftId())
                .setTime(param.getTime())
                .setType(param.getType().getValue(BusinessEvnEnum.HEI_YE))
                .setOperator(ConfigUtils.getServiceNameRequired())
                .build();
        Result<GiftNewManagerProto.ResponseSaveGiftTime> result = giftNewService.saveGiftTime(giftTime);
        if (RpcResult.isFail(result)) {
            log.error("HyGiftRemote createGiftTime fail, rCode: {}, giftTime: {}", result.rCode(), giftTime);
            return RpcResult.fail(result.rCode(), "创建礼物定时任务失败");
        }
        long timeId = result.target().getId();
        log.info("HyGiftRemote createGiftTime success, timeId: {}, giftTime: {}", timeId, giftTime);
        CreateGiftTimeResult createGiftTimeResult = new CreateGiftTimeResult();
        createGiftTimeResult.setId(timeId);
        return RpcResult.success(createGiftTimeResult);
    }

    @Override
    public Result<Void> deleteGiftTime(DeleteGiftTimeParam param) {
        log.info("HyGiftRemote deleteGiftTime param: {}", param);
        Long timeId = param.getTimeId();
        String operator = ConfigUtils.getServiceNameRequired();
        Result<GiftNewManagerProto.ResponseDeleteGiftTime> deleteGiftTimeResult = giftNewService.deleteGiftTime(timeId, operator);
        if (RpcResult.isFail(deleteGiftTimeResult)) {
            log.error("HyGiftRemote deleteGiftTime fail, rCode: {}, timeId: {}", deleteGiftTimeResult.rCode(), timeId);
            return RpcResult.fail(deleteGiftTimeResult.rCode(), "删除礼物定时任务失败");
        }
        log.info("HyGiftRemote deleteGiftTime success, timeId: {}", timeId);
        return RpcResult.success();
    }
}
