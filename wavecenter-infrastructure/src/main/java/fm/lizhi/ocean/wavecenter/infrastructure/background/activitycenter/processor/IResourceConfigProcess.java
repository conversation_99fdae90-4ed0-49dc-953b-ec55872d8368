package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.processor;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityResource;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityResource;
import fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityResourceConfig;
import fm.lizhi.ocean.wavecenter.base.processor.BusinessEnvAwareProcessor;

import java.util.List;

public interface IResourceConfigProcess extends BusinessEnvAwareProcessor {

    /**
     * 捆绑保存节目单
     *
     * @param resourceInfo 资源保存参数
     * @return 结果
     */
    void bindSaveProGramme(RequestSaveActivityResource resourceInfo);

    /**
     * 捆绑更新节目单
     *
     * @param resourceInfo 更新参数
     * @return 结果
     */
    void bindUpdateProGramme(RequestUpdateActivityResource resourceInfo);

    /**
     * 绑定删除节目单
     *
     * @param activityResourceConfig 删除参数
     * @param operator               操作人
     * @return 结果
     */
    void bindDeleteProGramme(ActivityResourceConfig activityResourceConfig, String operator);

    /**
     * 检查流量资源是否互斥
     * @param levelIds
     * @param sourceResourceCode
     * @param resourceType 资源类型
     * @return
     */
    Result<Void> checkResourceLevelRepeat(List<Long> levelIds, String sourceResourceCode, Integer resourceType);

    default Class<? extends BusinessEnvAwareProcessor> getBaseBusinessProcessor() {
        return IResourceConfigProcess.class;
    }

}
