package fm.lizhi.ocean.wavecenter.infrastructure.gift.remote;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.bean.PageBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.api.gift.bean.ListGiftBean;
import fm.lizhi.ocean.wavecenter.api.gift.request.RequestListGift;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.common.utils.JoinerUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.gift.convert.GiftInfraConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.gift.convert.XmGiftConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.gift.datastore.entity.GiftPo;
import fm.lizhi.ocean.wavecenter.infrastructure.gift.datastore.mapper.XmLiveGiftMapper;
import fm.lizhi.ocean.wavecenter.service.gift.dto.CreateGiftTimeParam;
import fm.lizhi.ocean.wavecenter.service.gift.dto.CreateGiftTimeResult;
import fm.lizhi.ocean.wavecenter.service.gift.dto.DeleteGiftTimeParam;
import fm.lizhi.ocean.wavecenter.service.gift.dto.GiftDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import xm.fm.lizhi.live.gift.api.GiftGroupService;
import xm.fm.lizhi.live.gift.api.GiftListService;
import xm.fm.lizhi.live.gift.api.GiftManagerService;
import xm.fm.lizhi.live.gift.api.GiftNewService;
import xm.fm.lizhi.live.gift.bean.dto.GiftExtraDto;
import xm.fm.lizhi.live.gift.constants.GiftType;
import xm.fm.lizhi.live.gift.enums.GiftRightTypeEnum;
import xm.fm.lizhi.live.gift.protocol.BeanProto;
import xm.fm.lizhi.live.gift.protocol.GiftGroupProto;
import xm.fm.lizhi.live.gift.protocol.GiftListProto;
import xm.fm.lizhi.live.gift.protocol.GiftNewProto;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/4/24 15:45
 */
@Slf4j
@Component
public class XmGiftRemote implements IGiftRemote{

    @Autowired
    private XmLiveGiftMapper liveGiftMapper;

    @Autowired
    private GiftNewService giftNewService;

    @Autowired
    private GiftListService giftListService;

    @Autowired
    private GiftManagerService giftManagerService;

    @Autowired
    private GiftGroupService giftGroupService;

    @Autowired
    private GiftInfraConvert giftInfraConvert;

    @Autowired
    private XmGiftConvert xmGiftConvert;

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.XIMI;
    }

    @Override
    public List<GiftDto> getByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        List<GiftPo> list = liveGiftMapper.getByIds(ids);
        return giftInfraConvert.toGiftDtoList(list, BusinessEvnEnum.XIMI.appId());
    }

    @Override
    public Result<PageBean<ListGiftBean>> listGift(RequestListGift param) {
        log.info("XmGiftRemote listGift param: {}", param);
        GiftNewProto.GiftListQueryRequest.Builder builder = GiftNewProto.GiftListQueryRequest.newBuilder();
        builder.addGiftType(GiftType.NEW_GIFT.getValue());
        builder.setCurrentPage(param.getPageNo());
        builder.setPageSize(param.getPageSize());
        Optional.ofNullable(param.getId()).ifPresent(builder::setGiftId);
        Optional.ofNullable(param.getName()).ifPresent(builder::setName);
        Optional.ofNullable(param.getStatus()).ifPresent(builder::setGiftStatus);
        GiftNewProto.GiftListQueryRequest request = builder.build();
        Result<GiftNewProto.ResponsePageQueryGiftListAndSort> result = giftNewService.pageQueryGiftListAndSort(request);
        if (RpcResult.isFail(result)) {
            return RpcResult.fail(result.rCode());
        }
        GiftNewProto.ResponsePageQueryGiftListAndSort response = result.target();
        PageBean<ListGiftBean> pageBean = xmGiftConvert.toListGiftPageBean(response);
        log.debug("XmGiftRemote listGift response: {}", response);
        return RpcResult.success(pageBean);
    }

    @Override
    public Result<Void> updateWhiteNjGroupId(long giftId, long groupId) {
        log.info("XmGiftRemote updateWhiteNjGroupId giftId: {}, groupId: {}", giftId, groupId);
        Result<GiftListProto.ResponseGetGift> getGiftResult = giftListService.getGift(giftId);
        if (RpcResult.isFail(getGiftResult)) {
            log.info("XmGiftRemote updateWhiteNjGroupId getGift fail giftId: {}, rCode: {}",
                    giftId, getGiftResult.rCode());
            return RpcResult.fail(getGiftResult.rCode(), "查询礼物失败, giftId: " + giftId);
        }
        BeanProto.Gift oldGift = getGiftResult.target().getGift();
        String oldGroupId = oldGift.getGiftAuthorityNjGroup();
        if (Objects.equals(oldGroupId, Long.toString(groupId))) {
            // 如果已经相等, 视为成功
            log.info("XmGiftRemote updateWhiteNjGroupId giftId: {}, groupId: {} already set", giftId, groupId);
            return RpcResult.success();
        }

        BeanProto.Gift.Builder newGiftBuilder = BeanProto.Gift.newBuilder(oldGift);
        newGiftBuilder.setGiftAuthorityNjGroup(Long.toString(groupId));
        // extra的格式可查看xm.fm.lizhi.live.gift.bean.dto.GiftExtraDto, 此处为了避免业务类变更导致字段丢失, 使用Map添加属性
        String oldExtra = oldGift.getExtra();
        if (StringUtils.isNotBlank(oldExtra)) {
            try {
                @SuppressWarnings("unchecked")
                Map<String, Object> oldExtraMap = (Map<String, Object>) JsonUtils.fromJsonString(oldExtra, Map.class);
                Map<String, Object> newExtraMap = new HashMap<>(oldExtraMap);
                GiftExtraDto oldGiftExtraDto = JsonUtils.fromJsonString(oldExtra, GiftExtraDto.class);
                // 只有当不是权限类礼物时, 才设置该字段
                int oldRightType = oldGiftExtraDto.getRightType();
                if (oldRightType != GiftRightTypeEnum.TYPE_GIFT_RIGHT.getValue() && oldRightType != GiftRightTypeEnum.TYPE_GIFT_TIME_RIGHT.getValue()) {
                    // 替换rightType字段为权限类礼物
                    newExtraMap.put("rightType", GiftRightTypeEnum.TYPE_GIFT_RIGHT.getValue());
                    newGiftBuilder.setExtra(JsonUtils.toJsonString(newExtraMap));
                }
            } catch (RuntimeException e) {
                log.error("XmGiftRemote updateWhiteNjGroupId parse oldExtra fail, giftId: {}, oldExtra: {}", giftId, oldExtra, e);
                return RpcResult.fail(CommonService.PARAM_ERROR, "解析礼物扩展信息失败");
            }
        } else {
            // 如果原来没有extra, 则直接添加
            GiftExtraDto giftExtraDto = new GiftExtraDto();
            giftExtraDto.setRightType(GiftRightTypeEnum.TYPE_GIFT_RIGHT.getValue());
            giftExtraDto.setBanners(Collections.emptyList());
            giftExtraDto.setTags(Collections.emptyList());
            newGiftBuilder.setExtra(JsonUtils.toJsonString(giftExtraDto));
        }
        BeanProto.Gift newGift = newGiftBuilder.build();

        Result<Void> editGiftResult = giftManagerService.editGift(newGift);
        if (RpcResult.isFail(editGiftResult)) {
            log.error("XmGiftRemote updateWhiteNjGroupId editGift fail, rCode: {}, newGift: {}",
                    editGiftResult.rCode(), newGift);
            return RpcResult.fail(editGiftResult.rCode(), "更新礼物白名单主播组ID失败, 礼物ID: " + giftId + ", 分组ID: " + groupId);
        }
        log.info("XmGiftRemote updateWhiteNjGroupId success giftId: {}, groupId: {}, oldGroupId: {}, oldExtra: {}, newGift: {}",
                giftId, groupId, oldGroupId, oldExtra, newGift);
        return RpcResult.success();
    }

    @Override
    public Result<Void> addGiftToGroup(long giftId, long groupId) {
        log.info("XmGiftRemote addGiftToGroup giftId: {}, groupId: {}", giftId, groupId);
        Result<BeanProto.GiftGroup> giftGroupResult = this.getGiftGroup(groupId);
        if (RpcResult.isFail(giftGroupResult)) {
            return RpcResult.fail(giftGroupResult.rCode(), giftGroupResult.getMessage());
        }
        BeanProto.GiftGroup oldGiftGroup = giftGroupResult.target();
        String oldRelationGift = oldGiftGroup.getRelationGift();
        List<Long> oldGiftIds = JoinerUtils.splitLong(oldRelationGift);
        if (oldGiftIds.contains(giftId)) {
            // 如果已经存在, 视为成功
            log.info("XmGiftRemote addGiftToGroup giftId: {}, groupId: {} already in group", giftId, groupId);
            return RpcResult.success();
        }
        ArrayList<Long> newGiftIds = new ArrayList<>(oldGiftIds);
        newGiftIds.add(giftId);
        String newRelationGift = JoinerUtils.join(newGiftIds);
        BeanProto.GiftGroup newGiftGroup = BeanProto.GiftGroup.newBuilder(oldGiftGroup)
                .setRelationGift(newRelationGift)
                .build();
        Result<Void> mergeGiftGroupResult = giftGroupService.mergeGiftGroup(newGiftGroup);
        if (RpcResult.isFail(mergeGiftGroupResult)) {
            log.error("XmGiftRemote addGiftToGroup mergeGiftGroup fail, rCode: {}, newGiftGroup: {}",
                    mergeGiftGroupResult.rCode(), newGiftGroup);
            return RpcResult.fail(mergeGiftGroupResult.rCode(), "添加礼物到分组失败, 礼物ID: " + giftId + ", 分组ID: " + groupId);
        }
        log.info("XmGiftRemote addGiftToGroup success, giftId: {}, groupId: {}", giftId, groupId);
        return RpcResult.success();
    }

    private Result<BeanProto.GiftGroup> getGiftGroup(long groupId) {
        // 财富等级传入最大值, 以保证查到分组
        Result<GiftGroupProto.ResponseGiftGroup> result = giftGroupService
                .queryGiftByGroup(Collections.singletonList(groupId), Integer.MAX_VALUE);
        if (RpcResult.isFail(result)) {
            log.error("HyGiftRemote getGiftGroup fail groupId: {}, rCode: {}", groupId, result.rCode());
            return RpcResult.fail(result.rCode(), "查询礼物分组失败, groupId: " + groupId);
        }
        for (BeanProto.GiftGroup giftGroup : result.target().getGiftGroupListList()) {
            if (Objects.equals(giftGroup.getId(), groupId)) {
                log.info("HyGiftRemote getGiftGroup groupId: {}, giftGroup: {}", groupId, giftGroup);
                return RpcResult.success(giftGroup);
            }
        }
        log.warn("HyGiftRemote getGiftGroup groupId: {}, giftGroup not found", groupId);
        return RpcResult.fail(CommonService.PARAM_ERROR, "礼物分组不存在");
    }

    @Override
    public Result<CreateGiftTimeResult> createGiftTime(CreateGiftTimeParam param) {
        log.info("XmGiftRemote createGiftTime param: {}", param);
        GiftNewProto.GiftTime giftTime = GiftNewProto.GiftTime.newBuilder()
                .setGiftId(param.getGiftId())
                .setTime(param.getTime())
                .setType(param.getType().getValue(BusinessEvnEnum.XIMI))
                .setOperator(ConfigUtils.getServiceNameRequired())
                .build();
        Result<GiftNewProto.ResponseSaveGiftTime> createGiftTimeResult = giftNewService.saveGiftTime(giftTime);
        if (RpcResult.isFail(createGiftTimeResult)) {
            log.error("XmGiftRemote createGiftTime fail, rCode: {}, giftTime: {}",
                    createGiftTimeResult.rCode(), giftTime);
            return RpcResult.fail(createGiftTimeResult.rCode(), "创建礼物定时任务失败");
        }
        long timeId = createGiftTimeResult.target().getId();
        log.info("XmGiftRemote createGiftTime success, timeId: {}, giftTime: {}", timeId, giftTime);
        CreateGiftTimeResult result = new CreateGiftTimeResult();
        result.setId(timeId);
        return RpcResult.success(result);
    }

    @Override
    public Result<Void> deleteGiftTime(DeleteGiftTimeParam param) {
        log.info("XmGiftRemote deleteGiftTime param: {}", param);
        Long timeId = param.getTimeId();
        String operator = ConfigUtils.getServiceNameRequired();
        Result<GiftNewProto.ResponseDeleteGiftTime> deleteGiftTimeResult = giftNewService.deleteGiftTime(timeId, operator);
        if (RpcResult.isFail(deleteGiftTimeResult)) {
            log.error("XmGiftRemote deleteGiftTime fail, rCode: {}, timeId: {}", deleteGiftTimeResult.rCode(), timeId);
            return RpcResult.fail(deleteGiftTimeResult.rCode(), "删除礼物定时任务失败");
        }
        log.info("XmGiftRemote deleteGiftTime success, timeId: {}", timeId);
        return RpcResult.success();
    }
}
