package fm.lizhi.ocean.wavecenter.infrastructure.gift.remote;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.bean.PageBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.api.gift.bean.ListGiftBean;
import fm.lizhi.ocean.wavecenter.api.gift.request.RequestListGift;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.JoinerUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.gift.convert.GiftInfraConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.gift.convert.PpGiftConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.gift.datastore.entity.GiftPo;
import fm.lizhi.ocean.wavecenter.infrastructure.gift.datastore.mapper.PpLiveGiftMapper;
import fm.lizhi.ocean.wavecenter.service.gift.dto.CreateGiftTimeParam;
import fm.lizhi.ocean.wavecenter.service.gift.dto.CreateGiftTimeResult;
import fm.lizhi.ocean.wavecenter.service.gift.dto.DeleteGiftTimeParam;
import fm.lizhi.ocean.wavecenter.service.gift.dto.GiftDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import pp.fm.lizhi.live.gift.api.GiftGroupService;
import pp.fm.lizhi.live.gift.api.GiftListService;
import pp.fm.lizhi.live.gift.api.GiftManagerService;
import pp.fm.lizhi.live.gift.api.GiftNewService;
import pp.fm.lizhi.live.gift.constants.GiftType;
import pp.fm.lizhi.live.gift.protocol.BeanProto;
import pp.fm.lizhi.live.gift.protocol.GiftGroupProto;
import pp.fm.lizhi.live.gift.protocol.GiftListProto;
import pp.fm.lizhi.live.gift.protocol.GiftNewProto;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/4/24 15:45
 */
@Slf4j
@Component
public class PpGiftRemote implements IGiftRemote{

    @Autowired
    private PpLiveGiftMapper liveGiftMapper;

    @Autowired
    private GiftNewService giftNewService;

    @Autowired
    private GiftListService giftListService;

    @Autowired
    private GiftManagerService giftManagerService;

    @Autowired
    private GiftGroupService giftGroupService;

    @Autowired
    private GiftInfraConvert giftInfraConvert;

    @Autowired
    private PpGiftConvert ppGiftConvert;

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.PP;
    }

    @Override
    public List<GiftDto> getByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        List<GiftPo> list = liveGiftMapper.getByIds(ids);
        return giftInfraConvert.toGiftDtoList(list, BusinessEvnEnum.PP.appId());
    }

    @Override
    public Result<PageBean<ListGiftBean>> listGift(RequestListGift param) {
        log.info("PpGiftRemote listGift param: {}", param);
        GiftNewProto.GiftListQueryRequest.Builder builder = GiftNewProto.GiftListQueryRequest.newBuilder();
        builder.addGiftType(GiftType.NEW_GIFT.getValue());
        builder.setCurrentPage(param.getPageNo());
        builder.setPageSize(param.getPageSize());
        Optional.ofNullable(param.getId()).ifPresent(builder::setGiftId);
        Optional.ofNullable(param.getName()).ifPresent(builder::setName);
        Optional.ofNullable(param.getStatus()).ifPresent(builder::setGiftStatus);
        GiftNewProto.GiftListQueryRequest request = builder.build();
        Result<GiftNewProto.ResponsePageQueryGiftListAndSort> result = giftNewService.pageQueryGiftListAndSort(request);
        if (RpcResult.isFail(result)) {
            return RpcResult.fail(result.rCode());
        }
        GiftNewProto.ResponsePageQueryGiftListAndSort response = result.target();
        log.debug("PpGiftRemote listGift response: {}", response);
        PageBean<ListGiftBean> pageBean = ppGiftConvert.toListGiftPageBean(response);
        return RpcResult.success(pageBean);
    }

    @Override
    public Result<Void> updateWhiteNjGroupId(long giftId, long groupId) {
        log.info("PpGiftRemote updateWhiteNjGroupId giftId: {}, groupId: {}", giftId, groupId);
        Result<GiftListProto.ResponseGetGift> getGiftResult = giftListService.getGift(giftId);
        if (RpcResult.isFail(getGiftResult)) {
            log.info("PpGiftRemote updateWhiteNjGroupId getGift fail giftId: {}, rCode: {}",
                    giftId, getGiftResult.rCode());
            return RpcResult.fail(getGiftResult.rCode(), "查询礼物失败, giftId: " + giftId);
        }
        BeanProto.Gift oldGift = getGiftResult.target().getGift();
        String oldGroupId = oldGift.getRoomVisibleUserGroup();
        if (Objects.equals(oldGroupId, Long.toString(groupId))) {
            // 如果已经相等, 视为成功
            log.info("PpGiftRemote updateWhiteNjGroupId giftId: {} already set groupId: {}", giftId, groupId);
            return RpcResult.success();
        }
        BeanProto.Gift newGift = BeanProto.Gift.newBuilder(oldGift)
                .setRoomVisibleUserGroup(Long.toString(groupId))
                .build();
        Result<Void> editGiftResult = giftManagerService.editGift(newGift);
        if (RpcResult.isFail(editGiftResult)) {
            log.error("PpGiftRemote updateWhiteNjGroupId editGift fail rCode: {}, newGift: {}",
                    editGiftResult.rCode(), newGift);
            return RpcResult.fail(editGiftResult.rCode(), "更新礼物白名单主播组ID失败, 礼物ID: " + giftId + ", 分组ID: " + groupId);
        }
        log.info("PpGiftRemote updateWhiteNjGroupId success giftId: {}, groupId: {}, oldGroupId: {}, newGift: {}",
                giftId, groupId, oldGroupId, newGift);
        return RpcResult.success();
    }

    @Override
    public Result<Void> addGiftToGroup(long giftId, long groupId) {
        log.info("PpGiftRemote addGiftToGroup giftId: {}, groupId: {}", giftId, groupId);
        Result<BeanProto.GiftGroup> giftGroupResult = this.getGiftGroup(groupId);
        if (RpcResult.isFail(giftGroupResult)) {
            return RpcResult.fail(giftGroupResult.rCode(), giftGroupResult.getMessage());
        }
        BeanProto.GiftGroup oldGiftGroup = giftGroupResult.target();
        String oldRelationGift = oldGiftGroup.getRelationGift();
        List<Long> oldGiftIds = JoinerUtils.splitLong(oldRelationGift);
        if (oldGiftIds.contains(giftId)) {
            // 如果已经存在, 视为成功
            log.info("PpGiftRemote addGiftToGroup giftId: {} already in groupId: {}", giftId, groupId);
            return RpcResult.success();
        }
        ArrayList<Long> newGiftIds = new ArrayList<>(oldGiftIds);
        newGiftIds.add(giftId);
        String newRelationGift = JoinerUtils.join(newGiftIds);
        BeanProto.GiftGroup newGiftGroup = BeanProto.GiftGroup.newBuilder(oldGiftGroup)
                .setRelationGift(newRelationGift)
                .build();
        Result<Void> mergeGiftGroupResult = giftGroupService.mergeGiftGroup(newGiftGroup);
        if (RpcResult.isFail(mergeGiftGroupResult)) {
            log.error("PpGiftRemote addGiftToGroup mergeGiftGroup fail, rCode: {}, newGiftGroup: {}",
                    mergeGiftGroupResult.rCode(), newGiftGroup);
            return RpcResult.fail(mergeGiftGroupResult.rCode(), "添加礼物到分组失败, 礼物ID: " + giftId + ", 分组ID: " + groupId);
        }
        log.info("PpGiftRemote addGiftToGroup success giftId: {}, groupId: {}", giftId, groupId);
        return RpcResult.success();
    }

    private Result<BeanProto.GiftGroup> getGiftGroup(long groupId) {
        // 财富等级传入最大值, 以保证查到分组
        Result<GiftGroupProto.ResponseGiftGroup> result = giftGroupService
                .queryGiftByGroup(Collections.singletonList(groupId), Integer.MAX_VALUE);
        if (RpcResult.isFail(result)) {
            log.error("HyGiftRemote getGiftGroup fail groupId: {}, rCode: {}", groupId, result.rCode());
            return RpcResult.fail(result.rCode(), "查询礼物分组失败, groupId: " + groupId);
        }
        for (BeanProto.GiftGroup giftGroup : result.target().getGiftGroupListList()) {
            if (Objects.equals(giftGroup.getId(), groupId)) {
                log.info("HyGiftRemote getGiftGroup groupId: {}, giftGroup: {}", groupId, giftGroup);
                return RpcResult.success(giftGroup);
            }
        }
        log.warn("HyGiftRemote getGiftGroup groupId: {}, giftGroup not found", groupId);
        return RpcResult.fail(CommonService.PARAM_ERROR, "礼物分组不存在");
    }

    @Override
    public Result<CreateGiftTimeResult> createGiftTime(CreateGiftTimeParam param) {
        log.info("PpGiftRemote createGiftTime param: {}", param);
        GiftNewProto.GiftTime giftTime = GiftNewProto.GiftTime.newBuilder()
                .setGiftId(param.getGiftId())
                .setTime(param.getTime())
                .setType(param.getType().getValue(BusinessEvnEnum.PP))
                .setOperator(ConfigUtils.getServiceNameRequired())
                .build();
        Result<GiftNewProto.ResponseSaveGiftTime> result = giftNewService.saveGiftTime(giftTime);
        if (RpcResult.isFail(result)) {
            log.error("PpGiftRemote createGiftTime fail, rCode: {}, giftTime: {}",
                    result.rCode(), giftTime);
            return RpcResult.fail(result.rCode(), "创建礼物定时任务失败");
        }
        long timeId = result.target().getId();
        log.info("PpGiftRemote createGiftTime success, timeId: {}, giftTime: {}", timeId, giftTime);
        CreateGiftTimeResult createGiftTimeResult = new CreateGiftTimeResult();
        createGiftTimeResult.setId(timeId);
        return RpcResult.success(createGiftTimeResult);
    }

    @Override
    public Result<Void> deleteGiftTime(DeleteGiftTimeParam param) {
        log.info("PpGiftRemote deleteGiftTime param: {}", param);
        Long timeId = param.getTimeId();
        String operator = ConfigUtils.getServiceNameRequired();
        Result<GiftNewProto.ResponseDeleteGiftTime> deleteGiftTimeResult = giftNewService.deleteGiftTime(timeId, operator);
        if (RpcResult.isFail(deleteGiftTimeResult)) {
            log.error("PpGiftRemote deleteGiftTime fail, rCode: {}, timeId: {}", deleteGiftTimeResult.rCode(), timeId);
            return RpcResult.fail(deleteGiftTimeResult.rCode(), "删除礼物定时任务失败");
        }
        log.info("PpGiftRemote deleteGiftTime success, timeId: {}", timeId);
        return RpcResult.success();
    }
}
