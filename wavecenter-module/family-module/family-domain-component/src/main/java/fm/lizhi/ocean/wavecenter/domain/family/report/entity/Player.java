package fm.lizhi.ocean.wavecenter.domain.family.report.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import fm.lizhi.ocean.wavecenter.domain.family.report.constant.SignStatusEnum;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.*;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.model.DeviceInfoTarget;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.model.PlayerBanTarget;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.model.PlayerSignTarget;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.model.VerifyInfoTarget;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.Objects;
import java.util.Optional;

/**
 * 陪玩对象
 */
@Data
@Slf4j
public class Player implements People {
    private Long id;

    private Integer appId;

    private String band;

    private String name;

    private String avatar;

    /**
     * 是否签约
     */
    private SignStatusEnum sign;

    /**
     * 签约时间
     */
    private Date signDate;

    /**
     * 解约时间 可能为空
     */
    private Date unSignDate;

    /**
     * 封禁信息
     */
    private PlayerBanInfo banInfo;

    /**
     * 签约厅 签约中or解约未满14天的厅
     */
    private Room signRoom;

    /**
     * 实名信息
     */
    private PlayerVerify verify;


    /**
     * 钱包
     */
    private PlayerWallet wallet;

    /**
     * 设备信息
     */
    private PlayerDevice device;

    // 搞个工厂方法
    public static Player create(Long id, Integer appId,
                                PlayerSignInfoRepository signInfoRepository,
                                PlayerBanInfo banInfo,
                                PlayerWallet playerWallet,
                                PlayerDevice playerDevice,
                                PlayerVerify verify) {
        Player player = new Player();
        player.id = id;
        player.appId = appId;
        PlayerSignTarget signInfo = signInfoRepository.getRecentSignInfo(appId, id);
        player.band = signInfo.getBand();
        player.name = signInfo.getName();
        player.avatar = signInfo.getAvatar();
        player.sign = signInfo.getSign();
        player.signDate = signInfo.getSignDate();
        player.unSignDate = signInfo.getUnSignDate();
        player.signRoom = signInfo.getSignRoom();
        player.wallet = playerWallet;
        player.device = playerDevice;
        player.banInfo = banInfo;
        player.verify = verify;
        return player;
    }

    public boolean isBanByWaveReport() {
        PlayerBanTarget playerBanTarget = banInfo.getPlayerBanInfo(appId, this.id);
        return playerBanTarget.isBan() && playerBanTarget.isBanByWaveReport() && playerBanTarget.isExistReportRecord();
    }


    //行为 是否签约
    public boolean isInSign() {
        return this.sign == SignStatusEnum.IN_EFFECT;
    }

    public boolean isSignCancel() {
        return this.sign == SignStatusEnum.UNSIGN;
    }

    /**
     * 曾经或者现在签约过
     *
     * @return
     */
    public boolean hasEverSigned() {
        return sign != SignStatusEnum.UNSIGNED;
    }

    //行为 是否解约未满14天
    public boolean isUnSignedLessThan14Days() {
        return isSignCancel() && (System.currentTimeMillis() - unSignDate.getTime()) < 14 * 24 * 60 * 60 * 1000;
    }


    @JsonIgnore
    public Optional<VerifyInfoTarget> getVerifyInfo() {
        log.info("获取陪玩{}的实名信息", this.id);
        return verify.getVerifyInfo(this.id);
    }

    public DeviceInfoTarget getDeviceInfo() {
        return device.getDeviceInfo(this.id);
    }

    /**
     * 是否有PC端设备信息
     *
     * @return boolean
     */
    public boolean hasPcDeviceInfo() {
        return getDeviceInfo().getPcDeviceId() != null;
    }

    /**
     * 是否有APP端设备信息
     *
     * @return boolean
     */
    public boolean hasAppDeviceInfo() {
        return getDeviceInfo().getAppDeviceId() != null;
    }

    /**
     * 对比自己最近一次实名信息，是否与other最近一次实名信息一致 一致返回true
     */
    @JsonIgnore
    public boolean isSameVerify(Player other) {
        log.info("对比陪玩{}的实名信息", this.id);
        Optional<VerifyInfoTarget> selfVerifyInfoOp = this.getVerifyInfo();
        Optional<VerifyInfoTarget> otherVerifyInfoOp = other.getVerifyInfo();

        if (!selfVerifyInfoOp.isPresent() || !otherVerifyInfoOp.isPresent()) {
            return false;
        }

        return Objects.equals(selfVerifyInfoOp.get().getIdCardNumber(), otherVerifyInfoOp.get().getIdCardNumber());
    }


    /**
     * 对比自己最近一次PC/APPIP，是否与other最近一次PC/app IP一致 一致返回true
     */
    public boolean isSameDeviceId(Player other) {
        log.info("对比陪玩{}的设备信息", this.id);
        DeviceInfoTarget deviceInfo = this.getDeviceInfo();
        DeviceInfoTarget otherDeviceInfo = other.getDeviceInfo();
        String appDeviceId = deviceInfo.getAppDeviceId();
        String pcDeviceId = deviceInfo.getPcDeviceId();

        String otherDeviceId = otherDeviceInfo.getAppDeviceId();
        String otherPcDeviceId = otherDeviceInfo.getPcDeviceId();
        if (appDeviceId != null && (appDeviceId.equals(otherDeviceId) || appDeviceId.equals(otherPcDeviceId))) {
            return true;
        }
        return pcDeviceId != null && (pcDeviceId.equals(otherDeviceId) || pcDeviceId.equals(otherPcDeviceId));
    }

    /**
     * 对比自己最近一次PC/APPIP，是否与other最近一次PC/app IP一致 一致返回true
     */
    public boolean isSameIp(Player other) {
        log.info("对比陪玩{}的IP信息", this.id);
        DeviceInfoTarget deviceInfo = this.getDeviceInfo();
        DeviceInfoTarget otherDeviceInfo = other.getDeviceInfo();

        String appCliIp = deviceInfo.getAppDeviceIp();
        String pcCliIp = deviceInfo.getPcDeviceIp();

        String otherAppCliIp = otherDeviceInfo.getAppDeviceIp();
        String otherPcCliIp = otherDeviceInfo.getPcDeviceIp();
        if (appCliIp != null && (appCliIp.equals(otherAppCliIp) || appCliIp.equals(otherPcCliIp))) {
            return true;
        }
        return pcCliIp != null && (pcCliIp.equals(otherAppCliIp) || pcCliIp.equals(otherPcCliIp));
    }

    public boolean isSameFamily(Room room) {
        if(hasEverSigned()) {
            return signRoom.isSameFamily(room);
        }
        return false;
    }

    public boolean isSigningInRoom(Long roomId) {
        return isInSign() && Objects.equals(signRoom.getId(), roomId);
    }
}
