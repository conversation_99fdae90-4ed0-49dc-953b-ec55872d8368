package fm.lizhi.ocean.wavecenter.domain.family.report.repository.model;

import fm.lizhi.ocean.wavecenter.domain.family.report.constant.SignStatusEnum;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.Room;
import lombok.Data;

import java.util.Date;

/**
 * 签约信息-值对象
 */
@Data
public class PlayerSignTarget {

    private String band;

    private String name;

    private String avatar;

    /**
     * 签约厅 签约中or最近一次解约的厅
     */
    private Room signRoom;


    /**
     * 签约状态
     */
    private SignStatusEnum sign;

    /**
     * 签约时间
     */
    private Date signDate;

    /**
     * 解约时间 可能为空
     */
    private Date unSignDate;

    public static PlayerSignTarget create(String band, String name, String url, Room signRoom, SignStatusEnum sign, Date signDate, Date unSignDate) {
        PlayerSignTarget playerSignTarget = new PlayerSignTarget();
        playerSignTarget.band = band;
        playerSignTarget.name = name;
        playerSignTarget.avatar = url;
        playerSignTarget.signRoom = signRoom;
        playerSignTarget.sign = sign;
        playerSignTarget.signDate = signDate;
        playerSignTarget.unSignDate = unSignDate;
        return playerSignTarget;
    }

    public static PlayerSignTarget createNoSign(String band, String name) {
        PlayerSignTarget playerSignTarget = new PlayerSignTarget();
        playerSignTarget.band = band;
        playerSignTarget.name = name;
        playerSignTarget.sign = SignStatusEnum.UNSIGNED;
        return playerSignTarget;
    }

    /**
     * 厅主
     */
    public static PlayerSignTarget createNj(String band, String name, Room signRoom) {
        PlayerSignTarget playerSignTarget = new PlayerSignTarget();
        playerSignTarget.band = band;
        playerSignTarget.name = name;
        playerSignTarget.signRoom = signRoom;
        playerSignTarget.sign = SignStatusEnum.UNSIGNED;
        return playerSignTarget;
    }

    /**
     * 厅主
     */
    public static PlayerSignTarget createFamilyUser(String band, String name, Long familyId) {
        PlayerSignTarget playerSignTarget = new PlayerSignTarget();
        playerSignTarget.band = band;
        playerSignTarget.name = name;
        playerSignTarget.sign = SignStatusEnum.UNSIGNED;
        playerSignTarget.signRoom = Room.create(0L, null, null, null, familyId);
        return playerSignTarget;
    }
}
