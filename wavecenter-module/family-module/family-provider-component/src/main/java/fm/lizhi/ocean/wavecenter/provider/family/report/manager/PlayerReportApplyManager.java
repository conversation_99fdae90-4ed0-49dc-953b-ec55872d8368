package fm.lizhi.ocean.wavecenter.provider.family.report.manager;

import com.google.common.collect.Lists;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.live.bean.AddAuditRecordFullParamBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.RedisLock;
import fm.lizhi.ocean.wavecenter.datastore.family.report.entity.*;
import fm.lizhi.ocean.wavecenter.datastore.family.report.mapper.ReportPlayerApplyRecordMapper;
import fm.lizhi.ocean.wavecenter.domain.family.report.constant.JudgmentStatusEnum;
import fm.lizhi.ocean.wavecenter.domain.family.report.constant.ReportTypeEnum;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.Player;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.Reporter;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.Room;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.apply.JudgeResult;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.apply.MultiAccountPlayerJobHopReportApply;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.apply.OriginalAccountPlayerJobHopReportApply;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.apply.PlayerJobHopReportApply;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.operate.OperateOrder;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.operate.ReportPlayerOperateItem;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.operate.ReportRoomOperateItem;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.*;
import fm.lizhi.ocean.wavecenter.domain.family.report.service.ReportJudgeService;
import fm.lizhi.ocean.wavecenter.domain.family.report.service.ReportOperateService;
import fm.lizhi.ocean.wavecenter.module.api.family.report.bean.ManualPlayerOperateResponse;
import fm.lizhi.ocean.wavecenter.module.api.family.report.bean.ManualRoomOperateResponse;
import fm.lizhi.ocean.wavecenter.module.api.family.report.bean.SubmitReportApplyResponse;
import fm.lizhi.ocean.wavecenter.module.api.family.report.constant.ReportApplyStatusEnum;
import fm.lizhi.ocean.wavecenter.module.api.family.report.constant.ReportOperateStatusEnum;
import fm.lizhi.ocean.wavecenter.module.api.family.report.request.SubmitReportApplyRequest;
import fm.lizhi.ocean.wavecenter.module.api.family.report.service.PlayerJobHopReportService;
import fm.lizhi.ocean.wavecenter.provider.family.report.config.PlayerReportConfig;
import fm.lizhi.ocean.wavecenter.provider.family.report.convert.PlayerReportApplyInfoConvert;
import fm.lizhi.ocean.wavecenter.provider.family.report.datastore.dao.ReportApplyDao;
import fm.lizhi.ocean.wavecenter.provider.family.report.datastore.mapper.ReportPlayerApplyRecordExtMapper;
import fm.lizhi.ocean.wavecenter.provider.family.report.kafka.message.ReportSyncAuditPunishMessage;
import fm.lizhi.ocean.wavecenter.provider.family.report.kafka.producer.ReportKafkaProducer;
import fm.lizhi.ocean.wavecenter.service.common.manager.IdManager;
import fm.lizhi.ocean.wavecenter.service.live.manager.LiveAuditManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class PlayerReportApplyManager {

    @Autowired
    private IdManager idManager;
    @Autowired
    private PlayerSignInfoRepository playerSignInfoRepository;
    @Autowired
    private OperateCommand operateCommand;
    @Autowired
    private PlayerWallet playerWallet;
    @Autowired
    private PlayerVerify playerVerify;
    @Autowired
    private PlayerReportApplyRepository playerReportApplyRepository;
    @Autowired
    private ReportJudgeService reportJudgeService;
    @Autowired
    private UserManager userManager;
    @Autowired
    private FamilyManager familyManager;
    @Autowired
    private PlayerReportApplyInfoConvert convert;

    @Autowired
    private ReportOperateService reportOperateService;
    @Autowired
    private ReportApplyDao reportApplyDao;

    @Autowired
    private PlayerReportRedisManager playerReportRedisManager;
    @Autowired
    private ReportPlayerApplyRecordExtMapper reportPlayerApplyRecordExtMapper;


    public SubmitReportApplyResponse submitPlayerJobHopReportApply(SubmitReportApplyRequest request, ReportTypeEnum reportType) {
        SubmitReportApplyResponse response = new SubmitReportApplyResponse();

        Integer appId = request.getAppId();
        Long accusedMainUserId = request.getAccusedMainUserId();
        Long accusedUserId = request.getAccusedUserId();
        Long accusedRoomId = request.getAccusedRoomId();
        Long reportUserId = request.getReportUserId();
        String source = request.getSource();
        UserInFamilyBean userInFamily = familyManager.getUserInFamily(accusedRoomId);

        long reportId = idManager.genId();
        Reporter reporter = Reporter.create(reportUserId, appId, playerSignInfoRepository);
        Room accusedRoom = buildAccusedRoom(accusedRoomId, userInFamily.getFamilyId());
        //构建举报单
        PlayerJobHopReportApply apply;
        if(reportType == ReportTypeEnum.JOB_HOPPING) {
            Player subPlayer = buildPlayer(appId, accusedUserId);
            Player mainPlayer = buildPlayer(appId, accusedMainUserId);
            apply = MultiAccountPlayerJobHopReportApply.create(reportId, appId, reporter, subPlayer, mainPlayer, accusedRoom, source);
        } else {
            Player originalPlayer = buildPlayer(appId, accusedUserId);
            apply = OriginalAccountPlayerJobHopReportApply.create(reportId, appId, reporter, originalPlayer, accusedRoom, source);
        }
        //保存订单
        apply.store(playerReportApplyRepository);
        //对举报单执行判定
        reportJudgeService.judgeJobHop(reportType, apply);
        if(!apply.isPass()) {
            response.setReportStatus(ReportApplyStatusEnum.FAILED.getStatus());
            response.setFailReason(apply.getJudgeResult().getReason());
            return response;
        }
        // 开罚单
        //按跳槽厅加锁
        try (RedisLock lock = playerReportRedisManager.tryGetReportRoomOperateLock(appId, accusedRoomId)) {
            if (!lock.tryLock()) {
                throw new RuntimeException("举报-厅-加锁失败；accusedRoomId=" + accusedRoomId);
            }
            //创建罚单
            OperateOrder operateOrder = OperateOrder.create(appId, idManager.genId(), idManager.genId(), apply, operateCommand, playerReportApplyRepository);
            //保存罚单
            operateOrder.store();
            //执行处罚
            operateOrder.execute();
            // TODO 可通过事件异步去发送通知
            apply.sendMessageAfterOperate(operateOrder);
            //构造返回
            fillResponse(response, operateOrder);
        } catch (Exception e) {
            //更新状态和原因
            playerReportApplyRepository.updateJudgeResult(apply.getId(), JudgeResult.createInterrupt());
            log.error("do submitPlayerJobHopReportApply failed", e);
            throw e;
        }
        return response;
    }

    private void fillResponse(SubmitReportApplyResponse response, OperateOrder operateOrder) {
        response.setReportStatus(ReportApplyStatusEnum.SUCCESS.getStatus());
        //过滤出自动的陪玩处罚 不管有没有成功
        List<ReportPlayerOperateItem> operateItems =
                operateOrder.getItems().stream().filter(ReportPlayerOperateItem::isAutoOperate).collect(Collectors.toList());
        response.setPlayerReportApplyOperateResult(convert.toPlayerReportApplyOperateResultBeanList(operateItems));

        //过滤出自动的跳槽厅处罚
        if(operateOrder.hasRoomAutoOperate()) {
            response.setRoomViolationRecord(convert.toRoomViolationRecordResp(operateOrder.getRoomViolation().getOperateItem()));
        }
    }

    @NotNull
    private Player buildPlayer(Integer appId, Long accusedUserId) {
        PlayerDevice playerDevice = new PlayerDeviceImpl(userManager);
        PlayerBanInfo banInfo = new PlayerBanInfoImpl(userManager, reportPlayerApplyRecordExtMapper);
        return Player.create(accusedUserId, appId, playerSignInfoRepository, banInfo, playerWallet, playerDevice, playerVerify);
    }

    @NotNull
    private Room buildAccusedRoom(Long accusedRoomId, Long userInFamily) {
        List<SimpleUserDto> accuseRoomInfo = userManager.getSimpleUserByIds(Lists.newArrayList(accusedRoomId));
        //构建跳槽厅
        return Room.create(accusedRoomId, accuseRoomInfo.get(0).getName(), accuseRoomInfo.get(0).getBand(),
                accuseRoomInfo.get(0).getAvatar(), userInFamily);
    }

    public ManualPlayerOperateResponse manualPlayerOperate(Long reportPlayerApplyOperateResultId, Integer appId, String operator) {
        ManualPlayerOperateResponse response = new ManualPlayerOperateResponse();
        response.setOperateStatusEnum(ReportOperateStatusEnum.FAIL);
        //获取操作记录
        ReportPlayerApplyOperateResult result = reportApplyDao.getReportPlayerApplyOperateResultById(reportPlayerApplyOperateResultId);
        if(result == null) {
            //打印详细日志
            log.warn("主播手动操作记录不存在;reportPlayerApplyOperateResultId={};appId={};operator{}", reportPlayerApplyOperateResultId, appId, operator);
            return response;
        }

        ReportOperateStatusEnum byCode = ReportOperateStatusEnum.getByCode(result.getStatus());
        response.setOperateStatusEnum(byCode);
        if(byCode != ReportOperateStatusEnum.WAIT_OPERATE) {
            log.warn("主播手动操作记录状态异常;reportPlayerApplyOperateResultId={};appId={};operator{}", reportPlayerApplyOperateResultId, appId, operator);
            return response;
        }
        //构造操作对象
        Player player = buildPlayer(appId, result.getOperatedPlayerId());
        ReportPlayerOperateItem reportPlayerOperateItem = convert.toReportPlayerOperateItem(result, player);
        ReportPlayerApplyRecord record = reportApplyDao.selectReportPlayerApplyRecordById(result.getPlayerReportApplyRecordId());
        if(record == null) {
            log.warn("举报-玩家-操作记录不存在;reportPlayerApplyOperateResultId={};appId={};operator{}", reportPlayerApplyOperateResultId, appId, operator);
            return response;
        }
        //执行
        reportOperateService.manualPlayerOperate(result.getPlayerReportApplyRecordId(), appId, reportPlayerOperateItem, operator, record.getAccusedRoomId(), record.getReportResultReason());
        if(reportPlayerOperateItem.isSuccess()) {
            response.setOperateStatusEnum(ReportOperateStatusEnum.SUCCESS);
        } else {
            response.setOperateStatusEnum(ReportOperateStatusEnum.FAIL);
        }
        return response;
    }

    public ManualRoomOperateResponse manualRoomOperate(Long reportRoomViolationOperateResultId, Integer appId, String operator, Integer operateDuration) {
        ManualRoomOperateResponse response = new ManualRoomOperateResponse();
        response.setOperateStatusEnum(ReportOperateStatusEnum.WAIT_OPERATE);
        ReportRoomViolationOperateResult result = reportApplyDao.getReportRoomViolationOperateResultById(reportRoomViolationOperateResultId);
        if(result == null) {
            log.warn("房间违规操作记录不存在;reportRoomViolationOperateResultId={};appId={};operator{}", reportRoomViolationOperateResultId, appId, operator);
            return response;
        }
        Long accusedRoomId = result.getAccusedRoomId();
        try (RedisLock lock = playerReportRedisManager.tryGetReportRoomOperateLock(appId, accusedRoomId)) {
            if (!lock.tryLock()) {
                throw new RuntimeException("手动封禁-厅-加锁失败；accusedRoomId=" + accusedRoomId);
            }

            ReportRoomViolationMapping mapping = reportApplyDao.getReportRoomViolationMappingByResultId(reportRoomViolationOperateResultId);
            if(mapping == null) {
                log.warn("房间违规操作记录映射不存在;reportRoomViolationOperateResultId={};appId={};operator{}", reportRoomViolationOperateResultId, appId, operator);
                return response;
            }
            ReportOperateStatusEnum byCode = ReportOperateStatusEnum.getByCode(result.getStatus());
            response.setOperateStatusEnum(byCode);

            if(byCode != ReportOperateStatusEnum.WAIT_OPERATE) {
                log.warn("房间违规操作记录状态异常;reportRoomViolationOperateResultId={};appId={};operator{}", reportRoomViolationOperateResultId, appId, operator);
                return response;
            }
            ReportPlayerApplyRecord record = reportApplyDao.selectReportPlayerApplyRecordById(mapping.getPlayerReportApplyRecordId());
            if(record == null) {
                log.warn("举报-厅-操作记录不存在;reportPlayerApplyOperateResultId={};appId={};operator{}", mapping.getPlayerReportApplyRecordId(), appId, operator);
                return response;
            }

            Long accusedFamilyId = result.getAccusedFamilyId();
            Room accusedRoom = buildAccusedRoom(accusedRoomId, accusedFamilyId);
            ReportRoomOperateItem reportRoomOperateItem = convert.toReportRoomOperateItem(result, accusedRoom, operateDuration);
            reportOperateService.manualRoomOperate(mapping.getPlayerReportApplyRecordId(), appId, reportRoomOperateItem, operator, record.getReportResultReason());
            if(reportRoomOperateItem.isSuccess()) {
                response.setOperateStatusEnum(ReportOperateStatusEnum.SUCCESS);
            } else {
                response.setOperateStatusEnum(ReportOperateStatusEnum.FAIL);
            }
        } catch (Exception e) {
            log.error("do manualRoomOperate failed", e);
            response.setOperateStatusEnum(ReportOperateStatusEnum.FAIL);
        }
        return response;
    }
}