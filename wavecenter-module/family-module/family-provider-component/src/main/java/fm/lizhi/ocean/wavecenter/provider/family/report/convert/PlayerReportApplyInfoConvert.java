package fm.lizhi.ocean.wavecenter.provider.family.report.convert;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.base.convert.CommonConvert;
import fm.lizhi.ocean.wavecenter.datastore.family.report.entity.*;
import fm.lizhi.ocean.wavecenter.domain.family.report.constant.ReportTypeEnum;
import fm.lizhi.ocean.wavecenter.domain.family.report.constant.SignStatusEnum;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.Player;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.Room;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.apply.MultiAccountPlayerJobHopReportApply;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.apply.OriginalAccountPlayerJobHopReportApply;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.apply.PlayerJobHopReportApply;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.operate.*;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.model.RoomViolationRecordValue;
import fm.lizhi.ocean.wavecenter.module.api.family.report.bean.*;
import fm.lizhi.ocean.wavecenter.provider.family.report.constant.PlayerReportConstant;
import fm.lizhi.ocean.wavecenter.provider.family.report.kafka.message.ReportSyncAuditPunishMessage;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.ERROR,
        uses = {
                CommonConvert.class,
        },
        imports = {Date.class, ConfigUtils.class, PlayerReportConstant.class}
)
public interface PlayerReportApplyInfoConvert {

    @Mapping(target = "id", source = "id")
    @Mapping(target = "reportType", source = "reportType.code")
    @Mapping(target = "appId", source = "appId")
    @Mapping(target = "reportUserId", source = "reportUser.id")
    @Mapping(target = "reportRoomId", source = "reportUser.signRoom.id")
    @Mapping(target = "reportFamilyId", source = "reportUser.signRoom.familyId")
    @Mapping(target = "accusedUserId", source = "accusedUser.id")
    @Mapping(target = "accusedRoomId", source = "accusedRoom.id")
    @Mapping(target = "accusedFamilyId", source = "accusedRoom.familyId")
    @Mapping(target = "accusedOriginalRoomId", ignore = true)
    @Mapping(target = "accusedOriginalFamilyId", ignore = true)
    @Mapping(target = "accusedUserSignStatus", expression = "java(buildAccusedUserSignStatus(apply.getAccusedUser(), apply.getAccusedRoom()))")
    @Mapping(target = "accusedUserSignTime", expression = "java(getSignTime(apply.getAccusedUser()))")
    @Mapping(target = "accusedMainUserId", source = "mainPlayer.id")
    @Mapping(target = "accusedMainRoomId", source = "mainPlayer.signRoom.id")
    @Mapping(target = "accusedMainFamilyId", source = "mainPlayer.signRoom.familyId")
    @Mapping(target = "accusedMainUserSignStatus", source = "mainPlayer.sign.code")
    @Mapping(target = "accusedMainUserSignTime", expression = "java(getSignTime(apply.getMainPlayer()))")
    @Mapping(target = "reportResultStatus", source = "judgeResult.judgmentStatus.code")
    @Mapping(target = "reportResultReason", source = "judgeResult.reason")
    @Mapping(target = "appDeviceId", expression = "java(apply.getAccusedUser().getDeviceInfo().getAppDeviceId())")
    @Mapping(target = "pcDeviceId", expression = "java(apply.getAccusedUser().getDeviceInfo().getPcDeviceId())")
    @Mapping(target = "deployEnv", expression = "java(ConfigUtils.getEnvRequired().name())")
    @Mapping(target = "operator", constant = "系统")
    @Mapping(target = "createTime", expression = "java(new Date())")
    @Mapping(target = "modifyTime", expression = "java(new Date())")
    ReportPlayerApplyRecord toJobHopReportPlayerApplyRecord(MultiAccountPlayerJobHopReportApply apply);

    default Integer buildAccusedUserSignStatus(Player accusedUser, Room accusedRoom) {
        if(!accusedUser.hasEverSigned()) {
            return SignStatusEnum.UNSIGNED.getCode();
        }
        //小号签约的厅不是跳槽厅
        if(!Objects.equals(accusedUser.getSignRoom().getId(), accusedRoom.getId())) {
            return SignStatusEnum.UNSIGNED.getCode();
        }
        return accusedUser.getSign().getCode();
    }


    @Mapping(target = "id", source = "id")
    @Mapping(target = "reportType", source = "reportType.code")
    @Mapping(target = "appId", source = "appId")
    @Mapping(target = "reportUserId", source = "reportUser.id")
    @Mapping(target = "reportRoomId", source = "reportUser.signRoom.id")
    @Mapping(target = "reportFamilyId", source = "reportUser.signRoom.familyId")
    @Mapping(target = "accusedUserId", source = "accusedUser.id")
    @Mapping(target = "accusedRoomId", source = "accusedRoom.id")
    @Mapping(target = "accusedFamilyId", source = "accusedRoom.familyId")
    @Mapping(target = "accusedOriginalRoomId", source = "poachedRoom.id")
    @Mapping(target = "accusedOriginalFamilyId", source = "poachedRoom.familyId")
    @Mapping(target = "accusedUserSignStatus", source = "accusedUser.sign.code")
    @Mapping(target = "accusedUserSignTime", expression = "java(getSignTime(apply.getAccusedUser()))")
    @Mapping(target = "accusedMainUserId", ignore = true)
    @Mapping(target = "accusedMainRoomId", ignore = true)
    @Mapping(target = "accusedMainFamilyId", ignore = true)
    @Mapping(target = "accusedMainUserSignStatus", ignore = true)
    @Mapping(target = "accusedMainUserSignTime", ignore = true)
    @Mapping(target = "reportResultStatus", source = "judgeResult.judgmentStatus.code")
    @Mapping(target = "reportResultReason", source = "judgeResult.reason")
    @Mapping(target = "appDeviceId", expression = "java(apply.getAccusedUser().getDeviceInfo().getAppDeviceId())")
    @Mapping(target = "pcDeviceId", expression = "java(apply.getAccusedUser().getDeviceInfo().getPcDeviceId())")
    @Mapping(target = "deployEnv", expression = "java(ConfigUtils.getEnvRequired().name())")
    @Mapping(target = "operator", constant = "系统")
    @Mapping(target = "createTime", expression = "java(new Date())")
    @Mapping(target = "modifyTime", expression = "java(new Date())")
    ReportPlayerApplyRecord toOriginalJobHopReportPlayerApplyRecord(OriginalAccountPlayerJobHopReportApply apply);



    default Date getSignTime(Player player) {
        switch (player.getSign()) {
            case UNSIGN:
                return player.getUnSignDate();
            case IN_EFFECT:
                return player.getSignDate();
            default:
                return null;
        }
    }



    @Mapping(target = "appId", source = "operateItem.operatedPlayer.appId")
    @Mapping(target = "operatedPlayerId", source = "operateItem.operatedPlayer.id")
    @Mapping(target = "reportType", source = "reportType")
    @Mapping(target = "operateObjType", source = "operateItem.operate.operateObjType.code")
    @Mapping(target = "operateType", source = "operateItem.operate.operateType.code")
    @Mapping(target = "operateDuration", source = "operateItem.operate.operateDuration")
    @Mapping(target = "manual", source = "operateItem.operate.manual")
    @Mapping(target = "status", source = "operateItem.status.code")
    @Mapping(target = "operator", constant = "system_auto")
    @Mapping(target = "deployEnv", expression = "java(ConfigUtils.getEnvRequired().name())")
    @Mapping(target = "createTime", source = "operateTime")
    @Mapping(target = "modifyTime", source = "operateTime")
    ReportPlayerApplyOperateResult toReportPlayerApplyOperateResult(ReportPlayerOperateItem operateItem, long playerReportApplyRecordId, Integer reportType, Date operateTime);

    @Mapping(target = "reachCondition", expression = "java(roomViolation.isReachCondition())")
    @Mapping(target = "id", source = "roomViolation.id")
    @Mapping(target = "appId", source = "roomViolation.appId")
    @Mapping(target = "count", source = "roomViolation.currentViolationUserSum")
    @Mapping(target = "conditionCount", source = "roomViolation.totalConditionCount")
    @Mapping(target = "punished", expression = "java(roomViolation.isRoomHasOperate())")
    @Mapping(target = "accusedUserId", source = "roomViolation.accusedUser.id")
    @Mapping(target = "accusedRoomId", source = "roomViolation.accusedRoom.id")
    @Mapping(target = "accusedFamilyId", source = "roomViolation.accusedRoom.familyId")
    @Mapping(target = "roomId", source = "roomViolation.poachedRoom.id")
    @Mapping(target = "familyId", source = "roomViolation.poachedRoom.familyId")
    @Mapping(target = "deployEnv", expression = "java(ConfigUtils.getEnvRequired().name())")
    @Mapping(target = "operator", constant = "系统")
    @Mapping(target = "createTime", source = "operateTime")
    @Mapping(target = "modifyTime", source = "operateTime")
    ReportRoomViolationRecord toReportRoomViolationRecord(RoomViolation roomViolation, long playerReportApplyRecordId, Integer reportType, Integer appId, Date operateTime);

    @Mapping(target = "id", source = "roomViolation.operateItem.id")
    @Mapping(target = "appId", source = "roomViolation.appId")
    @Mapping(target = "accusedRoomId", source = "roomViolation.accusedRoom.id")
    @Mapping(target = "accusedFamilyId", source = "roomViolation.accusedRoom.familyId")
    @Mapping(target = "operateSeq", source = "roomViolation.currentOperateSeq")
    @Mapping(target = "operateDuration", source = "roomViolation.operateItem.operate.operateDuration")
    @Mapping(target = "operateObjType", source = "roomViolation.operateItem.operate.operateObjType.code")
    @Mapping(target = "operateType", source = "roomViolation.operateItem.operate.operateType.code")
    @Mapping(target = "manual", source = "roomViolation.operateItem.operate.manual")
    @Mapping(target = "status", source = "roomViolation.operateItem.status.code")
    @Mapping(target = "operateRemark", ignore = true)
    @Mapping(target = "violationUserSum", source = "roomViolation.currentViolationUserSum")
    @Mapping(target = "operator", constant = "系统")
    @Mapping(target = "deployEnv", expression = "java(ConfigUtils.getEnvRequired().name())")
    @Mapping(target = "createTime", source = "operateTime")
    @Mapping(target = "modifyTime", source = "operateTime")
    ReportRoomViolationOperateResult toReportRoomViolationOperateResult(RoomViolation roomViolation, Date operateTime);


    @Mapping(target = "reachCondition", constant = "0")
    @Mapping(target = "roomViolationRecordId", source = "roomViolationRecord.id")
    @Mapping(target = "accusedUserId", source = "roomViolationRecord.accusedPlayerId")
    @Mapping(target = "createTime", source = "operateTime")
    ReportRoomViolationMapping toReportRoomViolationMapping(RoomViolationRecordValue roomViolationRecord,
                                                            long roomViolationOperateResultId,
                                                            long playerReportApplyRecordId,
                                                            Date operateTime);

    @Mapping(target = "roomViolationRecordId", source = "record.id")
    @Mapping(target = "reachCondition", constant = "1")
    @Mapping(target = "createTime", source = "operateTime")
    ReportRoomViolationMapping toReportRoomViolationMapping(ReportRoomViolationRecord record,
                                                            Long roomViolationOperateResultId,
                                                            long playerReportApplyRecordId,
                                                            Date operateTime);

    @Mapping(target = "accusedPlayerId", source = "accusedUserId")
    RoomViolationRecordValue toRoomViolationRecord(ReportRoomViolationRecord reportRoomViolationRecord);

    List<RoomViolationRecordValue> toRoomViolationRecordList(List<ReportRoomViolationRecord> reportRoomViolationRecords);


    @Mapping(target = "operateDuration", source = "operateItem.operate.operateDuration")
    @Mapping(target = "operateType", source = "operateItem.operate.operateType.code")
    @Mapping(target = "operateObjType", source = "operateItem.operate.operateObjType.code")
    PlayerReportApplyOperateResultBean toPlayerReportApplyOperateResultBean(ReportPlayerOperateItem operateItem);
    List<PlayerReportApplyOperateResultBean> toPlayerReportApplyOperateResultBeanList(List<ReportPlayerOperateItem> items);


    @Mapping(target = "photo", source = "avatar")
    OperatedPlayer toOperatedPlayer(Player player);
    @Mapping(target = "photo", source = "avatar")
    AccusedRoom toAccusedRoom(Room room);

    @Mapping(target = "accusedRoom", source = "operateItem.operatedRoom")
    @Mapping(target = "operateDuration", source = "operateItem.operate.operateDuration")
    @Mapping(target = "operateType", source = "operateItem.operate.operateType.code")
    @Mapping(target = "operateObjType", source = "operateItem.operate.operateObjType.code")
    RoomViolationRecord toRoomViolationRecordResp(ReportRoomOperateItem operateItem);


    @Mapping(target = "operateType", expression = "java(fm.lizhi.ocean.wavecenter.domain.family.report.constant.OperateTypeEnum.getByCode(result.getOperateType()))")
    @Mapping(target = "operateObjType", expression = "java(fm.lizhi.ocean.wavecenter.domain.family.report.constant.OperateObjTypeEnum.getByCode(result.getOperateObjType()))")
    Operate toOperate(ReportPlayerApplyOperateResult result);

    @Mapping(target = "operateType", expression = "java(fm.lizhi.ocean.wavecenter.domain.family.report.constant.OperateTypeEnum.getByCode(result.getOperateType()))")
    @Mapping(target = "operateObjType", expression = "java(fm.lizhi.ocean.wavecenter.domain.family.report.constant.OperateObjTypeEnum.getByCode(result.getOperateObjType()))")
    @Mapping(target = "operateDuration", source = "duration")
    Operate toOperate(ReportRoomViolationOperateResult result, Integer duration);

    @Mapping(target = "operate", expression = "java(toOperate(result))")
    @Mapping(target = "id", source = "result.id")
    ReportPlayerOperateItem toReportPlayerOperateItem(ReportPlayerApplyOperateResult result, Player operatedPlayer);

    @Mapping(target = "operateTime", ignore = true)
    @Mapping(target = "operate", expression = "java(toOperate(result, operateDuration))")
    @Mapping(target = "currentViolationUserSum", source = "result.violationUserSum")
    @Mapping(target = "currentOperateSeq", source = "result.operateSeq")
    @Mapping(target = "id", source = "result.id")
    ReportRoomOperateItem toReportRoomOperateItem(ReportRoomViolationOperateResult result, Room operatedRoom, Integer operateDuration);




    @Mapping(target = "toUserId", source = "apply.reportUser.id")
    @Mapping(target = "recJobHoppingLiveUserId", source = "apply.accusedRoom.id")
    @Mapping(target = "punishTime", expression = "java(buildPunishTime(operate))")
    @Mapping(target = "note", source = "apply.judgeResult.reason")
    @Mapping(target = "fromUserId", source = "apply.accusedUser.id")
    @Mapping(target = "contentId", expression = "java(apply.getId().toString())")
    @Mapping(target = "content", expression = "java(apply.getReportType().getDescription())")
    @Mapping(target = "bigUserId", source = "bigUserId")
    @Mapping(target = "appName", source = "appEnum.name")
    @Mapping(target = "reason", constant = PlayerReportConstant.PLAYER_REPORT_SUBMIT_AUDIT_REASON)
    ReportSyncAuditPunishMessage toReportSyncAuditPunishMessage(PlayerJobHopReportApply apply,
                                                                Long bigUserId,
                                                                Operate operate,
                                                                Integer type,
                                                                BusinessEvnEnum appEnum,
                                                                String op,
                                                                String belongId);

    @Mapping(target = "toUserId", source = "apply.reportUserId")
    @Mapping(target = "recJobHoppingLiveUserId", source = "apply.accusedRoomId")
    @Mapping(target = "punishTime", expression = "java(buildPunishTime(operate))")
    @Mapping(target = "note", source = "apply.reportResultReason")
    @Mapping(target = "fromUserId", source = "apply.accusedUserId")
    @Mapping(target = "contentId", expression = "java(apply.getId().toString())")
    @Mapping(target = "content", expression = "java(reportTypeEnum.getDescription())")
    @Mapping(target = "bigUserId", source = "apply.accusedMainUserId")
    @Mapping(target = "appName", source = "appEnum.name")
    @Mapping(target = "reason", constant = PlayerReportConstant.PLAYER_REPORT_SUBMIT_AUDIT_REASON)
    ReportSyncAuditPunishMessage toReportSyncAuditPunishMessage(ReportPlayerApplyRecord apply,
                                                                 ReportTypeEnum reportTypeEnum,
                                                                 Operate operate,
                                                                Integer type,
                                                                BusinessEvnEnum appEnum,
                                                                String op,
                                                                String belongId);


    default String buildPunishTime(Operate operate) {
        return operate.isPermanent() ? "永久" : operate.getOperateDuration() / 60 / 24 + "天";
    }

    default Long getAccusedUserSignNjId(Player accusedUser) {
        if(accusedUser.isInSign()) {
            return accusedUser.getSignRoom().getId();
        }
        return 0L;
    }

    default Long getAccusedUserFamilyId(Player accusedUser) {
        if(accusedUser.isInSign()) {
            return accusedUser.getSignRoom().getFamilyId();
        }
        return 0L;
    }

    @Mapping(target = "accusedNjUser", ignore = true)
    @Mapping(target = "accusedUser", ignore = true)
    ReportRoomViolationRecordBean toReportRoomViolationRecordBean(ReportRoomViolationRecord record);
}