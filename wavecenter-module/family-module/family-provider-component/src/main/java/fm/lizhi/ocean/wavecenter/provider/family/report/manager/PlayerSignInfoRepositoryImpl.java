package fm.lizhi.ocean.wavecenter.provider.family.report.manager;

import com.google.common.collect.Lists;
import fm.lizhi.ocean.wavecenter.api.sign.bean.FamilyAndNjContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.NjAndPlayerContractBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.domain.family.report.constant.SignStatusEnum;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.model.PlayerSignTarget;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.Room;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.PlayerSignInfoRepository;
import fm.lizhi.ocean.wavecenter.service.sign.manager.ContractManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.NonContractManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class PlayerSignInfoRepositoryImpl implements PlayerSignInfoRepository {

    @Autowired
    private ContractManager contractManager;

    @Autowired
    private NonContractManager nonContractManager;

    @Autowired
    private FamilyManager familyManager;

    @Autowired
    private UserManager userManager;

    @Override
    public PlayerSignTarget getRecentSignInfo(Integer appId, Long playerId) {
        List<SimpleUserDto> playerInfos = userManager.getSimpleUserByIds(Lists.newArrayList(playerId));
        SimpleUserDto userInfo = playerInfos.get(0);
        if(userInfo == null) {
            //不可能进来
            log.warn("getRecentSignInfo getSimpleUserMapByIds empty;appId={};playerId={}", appId,playerId);
            throw new RuntimeException("getRecentSignInfo getSimpleUserMapByIds empty playerId=" + playerId);
        }
        Long familyId;
        Long roomId;
        UserInFamilyBean userInFamily = familyManager.getUserInFamily(playerId);
        NjAndPlayerContractBean njContractBean;
        boolean sign;

        if(userInFamily == null || userInFamily.getFamilyId() == null) {
            //查询最近一次成功解约的签约记录
            Optional<NjAndPlayerContractBean> lastSuccessSign = nonContractManager.queryUserStopContractLast(playerId);
            if(!lastSuccessSign.isPresent()) {
                log.warn("getRecentSignInfo player queryUserStopContractLast empty after cancel;appId={};playerId={}", appId, playerId);
                return PlayerSignTarget.createNoSign(userInfo.getBand(), userInfo.getName());
            }
            njContractBean = lastSuccessSign.get();
            roomId = lastSuccessSign.get().getNjUserId();
            //过往签约成功后 生效时间取startTime
            //查询厅最近一条签约公会id 哪怕厅跟公会解约了
            Optional<FamilyAndNjContractBean> bean = contractManager.queryBestSign(roomId);
            if (!bean.isPresent()) {
                //不可能进来
                log.warn("getRecentSignInfo room queryBestSign empty after cancel;appId={};roomId={};playerId={}", appId, roomId, playerId);
//                throw new RuntimeException("getRecentSignInfo room  queryLastSign empty roomId=" + roomId);
                familyId = 0L;
            } else {
                familyId = bean.get().getFamilyId();
            }
            sign = false;
        } else {
            familyId = userInFamily.getFamilyId();
            roomId = userInFamily.getNjId();
            //厅主视为 未签约
            if(userInFamily.isRoom()) {
                List<SimpleUserDto> roomInfos = userManager.getSimpleUserByIds(Lists.newArrayList(roomId));
                SimpleUserDto roomInfo = roomInfos.get(0);
                Room signRoom = Room.create(roomId, roomInfo.getName(), roomInfo.getBand(), roomInfo.getAvatar(), familyId);
                return PlayerSignTarget.createNj(userInfo.getBand(), userInfo.getName(), signRoom);
            }
            //家族长 视为未签约
            else if(userInFamily.isFamily()) {
                return PlayerSignTarget.createFamilyUser(userInfo.getBand(), userInfo.getName(), familyId);
            }
            else if(userInFamily.isPlayer()) {
                //陪玩最近一次成功签约信息
                Optional<NjAndPlayerContractBean> lastSuccessSign = nonContractManager.queryLastSuccessSign(playerId, userInFamily.getNjId());
                if (!lastSuccessSign.isPresent()) {
                    //不可能进来
                    log.warn("getRecentSignInfo queryLastSuccessSign empty;appId={};familyId={};roomId={};playerId={}", appId, familyId, roomId, playerId);
                    return PlayerSignTarget.createNoSign(userInfo.getBand(), userInfo.getName());
                }
                njContractBean = lastSuccessSign.get();
                sign = true;
            }
            //既不是陪玩 又不是厅主 也不是家族长
            else {
                //不可能进来
                return PlayerSignTarget.createNoSign(userInfo.getBand(), userInfo.getName());
            }
        }

        List<SimpleUserDto> roomInfos = userManager.getSimpleUserByIds(Lists.newArrayList(roomId));
        SimpleUserDto roomInfo = roomInfos.get(0);
        Room signRoom = Room.create(roomId, roomInfo.getName(), roomInfo.getBand(), roomInfo.getAvatar(), familyId);

        return PlayerSignTarget.create(userInfo.getBand(),
                userInfo.getName(),
                userInfo.getAvatar(),
                signRoom,
                sign ? SignStatusEnum.IN_EFFECT : SignStatusEnum.UNSIGN,
                njContractBean.getStartTime(),
                njContractBean.getStopTime());
    }

}
