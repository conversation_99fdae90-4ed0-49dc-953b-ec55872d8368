package fm.lizhi.ocean.wavecenter.provider.family.report.manager;

import fm.lizhi.ocean.wavecenter.domain.family.report.repository.model.DeviceInfoTarget;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.PlayerDevice;
import fm.lizhi.ocean.wavecenter.service.user.dto.UserDeviceDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Optional;

@Slf4j
@Getter
public class PlayerDeviceImpl implements PlayerDevice {

    private  final UserManager userManager;

    private DeviceInfoTarget  result;

    public PlayerDeviceImpl(UserManager userManager) {
        this.userManager = userManager;
    }

    @Override
    public DeviceInfoTarget getDeviceInfo(Long playerId) {
        if(result != null) {
            return result;
        }
        result = new DeviceInfoTarget();
        Optional<UserDeviceDto> deviceDto = userManager.getLatestUserAppDeviceDto(playerId);
        if (deviceDto.isPresent()) {
            result.setAppDeviceId(deviceDto.get().getDeviceId());
            result.setAppDeviceIp(deviceDto.get().getIp());
        }
        Optional<UserDeviceDto> latestUserPcDeviceDto = userManager.getLatestUserPcDeviceDto(playerId);
        if (latestUserPcDeviceDto.isPresent()) {
            result.setPcDeviceId(latestUserPcDeviceDto.get().getDeviceId());
            result.setPcDeviceIp(latestUserPcDeviceDto.get().getIp());
        }
        log.info("getDeviceInfo userId={};result={}", playerId, result);
        return result;
    }
}
